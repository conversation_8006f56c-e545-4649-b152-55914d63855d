import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { upsertStripeAccountsDoc, setUserStripeId, upsertSellerFromStripeAccount, StripeAccountLike } from "@/services/stripeConnectAdminService";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const code = searchParams.get("code");
    const stateRaw = searchParams.get("state"); // May contain JSON with userId and returnUrl

    // State can be a plain userId (legacy) or a JSON string
    let userId: string | null = null;
    let returnUrlFromState: string | null = null;
    if (stateRaw) {
      try {
        const parsed = JSON.parse(stateRaw);
        userId = parsed.userId || null;
        returnUrlFromState = parsed.returnUrl || null;
      } catch {
        // Legacy format: state just the userId
        userId = stateRaw;
      }
    }
    const error = searchParams.get("error");

    // Handle OAuth errors (user canceled or denied)
    if (error) {
      const errorDescription =
        searchParams.get("error_description") || "OAuth authentication failed";
      console.error("OAuth error:", error, errorDescription);

      const origin = req.headers.get("origin");
      const baseReturn = returnUrlFromState || `${origin}/`;
      const redirectUrl = new URL(baseReturn, origin);
      // Provide a non-blocking hint for the UI (optional)
      redirectUrl.searchParams.set("connect", "denied");
      return NextResponse.redirect(redirectUrl.toString());
    }

    if (!code || !userId) {
      return NextResponse.json(
        { error: "Missing authorization code or user state" },
        { status: 400 }
      );
    }

    console.log("Processing OAuth callback for user:", userId, "with code:", code);

    // Exchange authorization code for access token and account info
    const response = await stripe.oauth.token({
      grant_type: "authorization_code",
      code: code,
    });

    const {
      access_token,
      refresh_token,
      token_type,
      stripe_publishable_key,
      stripe_user_id,
      scope,
    } = response;

    console.log("OAuth token exchange successful for account:", stripe_user_id);

    if (!stripe_user_id) {
      return NextResponse.json({ error: "No Stripe account ID received" }, { status: 400 });
    }

    // Get detailed account information
    const account = await stripe.accounts.retrieve(stripe_user_id);

    // Save account details via Firebase Admin SDK
    try {
      await upsertStripeAccountsDoc({
        accountId: stripe_user_id,
        userId: userId,
        account: account as StripeAccountLike,
        extra: {
          accessToken: access_token,
          refreshToken: refresh_token,
          publishableKey: stripe_publishable_key,
          tokenType: token_type,
          scope: scope,
          email: account.email,
          businessName: account.business_profile?.name,
          connectedAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
          country: account.country,
          currency: account.default_currency,
          accountType: account.type,
          businessType: account.business_type,
        },
      });

      await setUserStripeId({ userId, stripeAccountId: stripe_user_id });
      await upsertSellerFromStripeAccount({ userId, account: account as StripeAccountLike });

      console.log("Saved OAuth account details via Admin SDK:", {
        userId,
        accountId: stripe_user_id,
        email: account.email,
      });
    } catch (firebaseError) {
      console.error("Error saving OAuth details via Admin SDK:", firebaseError);
      // Continue with redirect even if Firebase save fails
    }

    // Redirect back to returnUrl if provided; otherwise, home page
    const origin = req.headers.get("origin") || "http://localhost:3000";
    const baseReturn = returnUrlFromState || `${origin}/`;
    const redirectUrl = new URL(baseReturn, origin);
    redirectUrl.searchParams.set("connected", "true");
    redirectUrl.searchParams.set("account", stripe_user_id);
    redirectUrl.searchParams.set("userId", userId);
    redirectUrl.searchParams.set("tab", "connect");
    redirectUrl.searchParams.set("email", account.email || "");
    redirectUrl.searchParams.set("business", account.business_profile?.name || "");

    return NextResponse.redirect(redirectUrl.toString());
  } catch (error) {
    console.error("Error in OAuth callback:", error);

    const origin = req.headers.get("origin") || "http://localhost:3000";
    const redirectUrl = new URL(`${origin}/`);
    redirectUrl.searchParams.set("connect", "error");
    return NextResponse.redirect(redirectUrl.toString());
  }
}
